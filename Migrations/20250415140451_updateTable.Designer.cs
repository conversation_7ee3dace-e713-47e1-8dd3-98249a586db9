﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace api.coleta.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250415140451_updateTable")]
    partial class updateTable
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.2")
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            MySqlModelBuilderExtensions.AutoIncrementColumns(modelBuilder);

            modelBuilder.Entity("api.coleta.Models.Entidades.Cliente", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("CPF")
                        .IsRequired()
                        .HasMaxLength(14)
                        .HasColumnType("varchar(14)");

                    b.Property<string>("Cep")
                        .IsRequired()
                        .HasMaxLength(8)
                        .HasColumnType("varchar(8)");

                    b.Property<string>("Cidade")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime>("DataInclusao")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Endereco")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("Estado")
                        .IsRequired()
                        .HasMaxLength(2)
                        .HasColumnType("varchar(2)");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("Telefone")
                        .IsRequired()
                        .HasMaxLength(11)
                        .HasColumnType("varchar(11)");

                    b.Property<Guid>("UsuarioID")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("UsuarioID");

                    b.ToTable("Clientes");
                });

            modelBuilder.Entity("api.coleta.Models.Entidades.Coleta", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("DataInclusao")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid>("GeojsonID")
                        .HasColumnType("char(36)");

                    b.Property<string>("Observacao")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<int>("Profundidade")
                        .HasColumnType("int");

                    b.Property<Guid>("TalhaoID")
                        .HasColumnType("char(36)");

                    b.Property<int>("TipoAnalise")
                        .HasColumnType("int");

                    b.Property<int>("TipoColeta")
                        .HasColumnType("int");

                    b.Property<Guid>("UsuarioID")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("UsuarioRespID")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("GeojsonID");

                    b.HasIndex("TalhaoID");

                    b.HasIndex("UsuarioID");

                    b.HasIndex("UsuarioRespID");

                    b.ToTable("Coletas");
                });

            modelBuilder.Entity("api.coleta.Models.Entidades.ConfiguracaoPadrao", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("CorHex")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.Property<DateTime>("DataInclusao")
                        .HasColumnType("datetime(6)");

                    b.Property<decimal>("Limite")
                        .HasColumnType("decimal(12,4)");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.HasKey("Id");

                    b.ToTable("ConfiguracaoPadraos");
                });

            modelBuilder.Entity("api.coleta.Models.Entidades.ConfiguracaoPersonalizada", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("CorHex")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.Property<DateTime>("DataInclusao")
                        .HasColumnType("datetime(6)");

                    b.Property<decimal>("Limite")
                        .HasColumnType("decimal(12,4)");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<Guid>("UsuarioId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("UsuarioId");

                    b.ToTable("ConfiguracaoPersonalizadas");
                });

            modelBuilder.Entity("api.coleta.Models.Entidades.Geojson", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("DataInclusao")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Grid")
                        .IsRequired()
                        .HasColumnType("JSON");

                    b.Property<string>("Pontos")
                        .IsRequired()
                        .HasColumnType("JSON");

                    b.HasKey("Id");

                    b.ToTable("Geojson");
                });

            modelBuilder.Entity("api.coleta.Models.Entidades.MColeta", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("DataInclusao")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Endereco")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<double>("Lat")
                        .HasColumnType("double");

                    b.Property<double>("Lng")
                        .HasColumnType("double");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("Place_Id")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("Reference")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.HasKey("Id");

                    b.ToTable("MColetas");
                });

            modelBuilder.Entity("api.coleta.Models.Entidades.Minerais", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("DataInclusao")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.HasKey("Id");

                    b.ToTable("Minerais");
                });

            modelBuilder.Entity("api.coleta.Models.Entidades.Relatorio", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<Guid>("ColetaId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("DataInclusao")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("JsonRelatorio")
                        .IsRequired()
                        .HasColumnType("JSON");

                    b.Property<string>("LinkBackup")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<Guid>("UsuarioId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("ColetaId");

                    b.HasIndex("UsuarioId");

                    b.ToTable("Relatorios");
                });

            modelBuilder.Entity("api.coleta.Models.Entidades.Safra", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<Guid>("ClienteID")
                        .HasColumnType("char(36)");

                    b.Property<DateTime?>("DataFim")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime>("DataInclusao")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime>("DataInicio")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid>("FazendaID")
                        .HasColumnType("char(36)");

                    b.Property<string>("Observacao")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<Guid>("UsuarioID")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("ClienteID");

                    b.HasIndex("FazendaID");

                    b.HasIndex("UsuarioID");

                    b.ToTable("Safras");
                });

            modelBuilder.Entity("api.coleta.Models.Entidades.Talhao", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<Guid>("ClienteID")
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("DataInclusao")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid>("FazendaID")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("UsuarioID")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("ClienteID");

                    b.HasIndex("FazendaID");

                    b.HasIndex("UsuarioID");

                    b.ToTable("Talhoes");
                });

            modelBuilder.Entity("api.coleta.Models.Entidades.TalhaoJson", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("Area")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("Coordenadas")
                        .IsRequired()
                        .HasColumnType("JSON");

                    b.Property<DateTime>("DataInclusao")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("Observacao")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<Guid>("TalhaoID")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("TalhaoID");

                    b.ToTable("TalhaoJson");
                });

            modelBuilder.Entity("api.coleta.Models.Entidades.Usuario", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("CPF")
                        .IsRequired()
                        .HasMaxLength(11)
                        .HasColumnType("varchar(11)");

                    b.Property<DateTime>("DataInclusao")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("NomeCompleto")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Senha")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Telefone")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<Guid?>("adminId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("adminId");

                    b.ToTable("Usuarios");
                });

            modelBuilder.Entity("api.coleta.Models.Entidades.VinculoClienteFazenda", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<bool>("Ativo")
                        .HasColumnType("tinyint(1)");

                    b.Property<Guid>("ClienteId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("DataInclusao")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid>("FazendaId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("ClienteId");

                    b.HasIndex("FazendaId");

                    b.ToTable("VinculoClienteFazendas");
                });

            modelBuilder.Entity("api.fazenda.Models.Entidades.Fazenda", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<Guid>("ClienteID")
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("DataInclusao")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Endereco")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)");

                    b.Property<double>("Lat")
                        .HasMaxLength(12)
                        .HasColumnType("double");

                    b.Property<double>("Lng")
                        .HasMaxLength(12)
                        .HasColumnType("double");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<Guid>("UsuarioID")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("ClienteID");

                    b.HasIndex("UsuarioID");

                    b.ToTable("Fazendas");
                });

            modelBuilder.Entity("api.coleta.Models.Entidades.Cliente", b =>
                {
                    b.HasOne("api.coleta.Models.Entidades.Usuario", "Usuario")
                        .WithMany()
                        .HasForeignKey("UsuarioID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Usuario");
                });

            modelBuilder.Entity("api.coleta.Models.Entidades.Coleta", b =>
                {
                    b.HasOne("api.coleta.Models.Entidades.Geojson", "Geojson")
                        .WithMany()
                        .HasForeignKey("GeojsonID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("api.coleta.Models.Entidades.TalhaoJson", "Talhao")
                        .WithMany()
                        .HasForeignKey("TalhaoID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("api.coleta.Models.Entidades.Usuario", "Usuario")
                        .WithMany()
                        .HasForeignKey("UsuarioID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("api.coleta.Models.Entidades.Usuario", "UsuarioResp")
                        .WithMany()
                        .HasForeignKey("UsuarioRespID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Geojson");

                    b.Navigation("Talhao");

                    b.Navigation("Usuario");

                    b.Navigation("UsuarioResp");
                });

            modelBuilder.Entity("api.coleta.Models.Entidades.ConfiguracaoPersonalizada", b =>
                {
                    b.HasOne("api.coleta.Models.Entidades.Usuario", "Usuario")
                        .WithMany()
                        .HasForeignKey("UsuarioId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Usuario");
                });

            modelBuilder.Entity("api.coleta.Models.Entidades.Relatorio", b =>
                {
                    b.HasOne("api.coleta.Models.Entidades.Coleta", "Coleta")
                        .WithMany()
                        .HasForeignKey("ColetaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("api.coleta.Models.Entidades.Usuario", "Usuario")
                        .WithMany()
                        .HasForeignKey("UsuarioId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Coleta");

                    b.Navigation("Usuario");
                });

            modelBuilder.Entity("api.coleta.Models.Entidades.Safra", b =>
                {
                    b.HasOne("api.coleta.Models.Entidades.Cliente", "Cliente")
                        .WithMany()
                        .HasForeignKey("ClienteID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("api.fazenda.Models.Entidades.Fazenda", "Fazenda")
                        .WithMany()
                        .HasForeignKey("FazendaID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("api.coleta.Models.Entidades.Usuario", "Usuario")
                        .WithMany()
                        .HasForeignKey("UsuarioID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Cliente");

                    b.Navigation("Fazenda");

                    b.Navigation("Usuario");
                });

            modelBuilder.Entity("api.coleta.Models.Entidades.Talhao", b =>
                {
                    b.HasOne("api.coleta.Models.Entidades.Cliente", "Cliente")
                        .WithMany()
                        .HasForeignKey("ClienteID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("api.fazenda.Models.Entidades.Fazenda", "Fazenda")
                        .WithMany()
                        .HasForeignKey("FazendaID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("api.coleta.Models.Entidades.Usuario", "Usuario")
                        .WithMany()
                        .HasForeignKey("UsuarioID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Cliente");

                    b.Navigation("Fazenda");

                    b.Navigation("Usuario");
                });

            modelBuilder.Entity("api.coleta.Models.Entidades.TalhaoJson", b =>
                {
                    b.HasOne("api.coleta.Models.Entidades.Talhao", "Talhao")
                        .WithMany()
                        .HasForeignKey("TalhaoID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Talhao");
                });

            modelBuilder.Entity("api.coleta.Models.Entidades.Usuario", b =>
                {
                    b.HasOne("api.coleta.Models.Entidades.Usuario", "Admin")
                        .WithMany()
                        .HasForeignKey("adminId");

                    b.Navigation("Admin");
                });

            modelBuilder.Entity("api.coleta.Models.Entidades.VinculoClienteFazenda", b =>
                {
                    b.HasOne("api.coleta.Models.Entidades.Cliente", "Cliente")
                        .WithMany()
                        .HasForeignKey("ClienteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("api.fazenda.Models.Entidades.Fazenda", "Fazenda")
                        .WithMany()
                        .HasForeignKey("FazendaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Cliente");

                    b.Navigation("Fazenda");
                });

            modelBuilder.Entity("api.fazenda.Models.Entidades.Fazenda", b =>
                {
                    b.HasOne("api.coleta.Models.Entidades.Cliente", "Cliente")
                        .WithMany()
                        .HasForeignKey("ClienteID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("api.coleta.Models.Entidades.Usuario", "Usuario")
                        .WithMany()
                        .HasForeignKey("UsuarioID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Cliente");

                    b.Navigation("Usuario");
                });
#pragma warning restore 612, 618
        }
    }
}
